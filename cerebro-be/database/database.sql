-- Charset y otras opciones recomendadas a nivel de sesión (opcional)
SET NAMES utf8mb4;

-- Tabla para almacenar los clientes OAuth
CREATE TABLE IF NOT EXISTS oauth_clients (
  id              VARCHAR(255) NOT NULL PRIMARY KEY,
  client_id       VARCHAR(255) NOT NULL UNIQUE,
  client_secret    VARCHAR(255) NOT NULL,
  name            VARCHAR(100) NOT NULL,
  description     TEXT NULL,
  redirect_uris   LONGTEXT NULL,
  scope           VARCHAR(255) NOT NULL DEFAULT 'read',
  grant_types     LONGTEXT NOT NULL DEFAULT '[]',
  is_active       TINYINT(1) NOT NULL DEFAULT 1,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  CONSTRAINT chk_oauth_clients_redirect_uris_json CHECK (redirect_uris IS NULL OR JSON_VALID(redirect_uris)),
  CONSTRAINT chk_oauth_clients_grant_types_json  CHECK (JSON_VALID(grant_types))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabla para almacenar los access tokens
CREATE TABLE IF NOT EXISTS access_tokens (
  id           VARCHAR(255) NOT NULL PRIMARY KEY,
  token        VARCHAR(500) NOT NULL UNIQUE,
  client_id    VARCHAR(255) NOT NULL,
  user_id      VARCHAR(255) NULL,
  scope        VARCHAR(255) NOT NULL,
  expires_at   DATETIME NOT NULL,
  created_at   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

  CONSTRAINT fk_access_tokens_client
    FOREIGN KEY (client_id) REFERENCES oauth_clients(id)
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabla para almacenar los refresh tokens
CREATE TABLE IF NOT EXISTS refresh_tokens (
  id           VARCHAR(255) NOT NULL PRIMARY KEY,
  token        VARCHAR(500) NOT NULL UNIQUE,
  client_id    VARCHAR(255) NOT NULL,
  user_id      VARCHAR(255) NULL,
  expires_at   DATETIME NOT NULL,
  created_at   DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

  CONSTRAINT fk_refresh_tokens_client
    FOREIGN KEY (client_id) REFERENCES oauth_clients(id)
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabla para almacenar los servicios monitoreados
CREATE TABLE IF NOT EXISTS monitored_services (
  id              INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  name            VARCHAR(100) NOT NULL,
  container_name  VARCHAR(100) NOT NULL UNIQUE,
  url             VARCHAR(255) NULL,
  icon            VARCHAR(10) NOT NULL DEFAULT '🔧',
  description     TEXT NOT NULL,
  is_active       TINYINT(1) NOT NULL DEFAULT 1,
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  KEY idx_container_name (container_name),
  KEY idx_is_active (is_active),
  KEY idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabla para almacenar el historial de estados de los servicios
CREATE TABLE IF NOT EXISTS service_status_history (
  id             BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  service_id     INT NOT NULL,
  status         VARCHAR(50) NOT NULL,
  state          VARCHAR(50) NOT NULL,
  uptime         VARCHAR(255) NULL,
  health         VARCHAR(50) NULL,
  error_message  TEXT NULL,
  checked_at     DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

  KEY idx_service_id (service_id),
  KEY idx_checked_at (checked_at),
  KEY idx_status (status),

  CONSTRAINT fk_service_status_history_service
    FOREIGN KEY (service_id) REFERENCES monitored_services(id)
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabla de roles
CREATE TABLE IF NOT EXISTS roles (
  id          INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  slug        VARCHAR(50) NOT NULL UNIQUE,
  name        VARCHAR(50) NOT NULL UNIQUE,
  description TEXT NULL,
  created_at  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabla de usuarios
CREATE TABLE IF NOT EXISTS users (
  id          VARCHAR(255) NOT NULL PRIMARY KEY,
  email       VARCHAR(255) NOT NULL UNIQUE,
  name        VARCHAR(100) NULL,
  password    VARCHAR(255) NOT NULL,
  role_id     INT NULL,
  is_active   TINYINT(1) NOT NULL DEFAULT 1,
  created_at  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at  DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login_at DATETIME NULL,
  password_changed_at DATETIME NULL,

  CONSTRAINT chk_users_email_format CHECK (email LIKE '%@%.__%')
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- access_tokens.user_id → users(id)
ALTER TABLE access_tokens
  ADD INDEX IF NOT EXISTS idx_access_tokens_user_id (user_id),
  DROP FOREIGN KEY IF EXISTS fk_access_tokens_user,
  ADD CONSTRAINT fk_access_tokens_user
    FOREIGN KEY (user_id) REFERENCES users(id)
    ON DELETE SET NULL
    ON UPDATE CASCADE;

-- refresh_tokens.user_id → users(id)
ALTER TABLE refresh_tokens
  ADD INDEX IF NOT EXISTS idx_refresh_tokens_user_id (user_id),
  DROP FOREIGN KEY IF EXISTS fk_refresh_tokens_user,
  ADD CONSTRAINT fk_refresh_tokens_user
    FOREIGN KEY (user_id) REFERENCES users(id)
    ON DELETE SET NULL
    ON UPDATE CASCADE;

-- (Opcional) Reafirmar la FK de users.role_id con política clara
ALTER TABLE users
  ADD INDEX IF NOT EXISTS idx_users_role_id (role_id),
  DROP FOREIGN KEY IF EXISTS fk_users_role,
  ADD CONSTRAINT fk_users_role
    FOREIGN KEY (role_id) REFERENCES roles(id)
    ON DELETE RESTRICT
    ON UPDATE CASCADE;

INSERT INTO roles (name, slug, description)
VALUES
  ('USER','user','Usuario estándar'),
  ('ADMIN','admin','Administrador del sistema'),
  ('SERVICE','service','Cuenta de servicio/daemon')
ON DUPLICATE KEY UPDATE
  name = VALUES(name),
  description = VALUES(description);

SET @role_admin := (SELECT id FROM roles WHERE slug='admin' LIMIT 1);
SET @user_admin_id := REPLACE(UUID(),'-','');

-- Hash bcrypt de ejemplo (NO usar en producción tal cual)
-- "Admin123!" -> $2y$12$Q2XHk3P1s7VvO4k5S2rFwe4t0a9a4C2o0YdWmZk7yq7e8y0nqYtQ6
-- Genera el tuyo y sustitúyelo.
INSERT INTO users (id, email, name, password, role_id, is_active)
VALUES
(@user_admin_id, '<EMAIL>', 'Super Admin',
 '$2y$12$Q2XHk3P1s7VvO4k5S2rFwe4t0a9a4C2o0YdWmZk7yq7e8y0nqYtQ6',
 @role_admin, 1)
ON DUPLICATE KEY UPDATE
  name = VALUES(name),
  role_id = VALUES(role_id),
  is_active = VALUES(is_active);

-- Variables comunes
SET @now := NOW();

-- Generar IDs
SET @cli_bitcoin   := REPLACE(UUID(),'-','');
SET @cli_abogados  := REPLACE(UUID(),'-','');
SET @cli_cerebrofe := REPLACE(UUID(),'-','');

-- Ejemplo de "secreto" hasheado (sustituye por tu hash o por un valor temporal)
SET @secret_hash := '$2y$12$4X9sJ1e6rjQf8w2FQx1bne1x0o1vM2m3n4o5p6q7r8s9t0u1v2w3a';

INSERT INTO oauth_clients
(id, client_id, client_secret, name, description, redirect_uris, scope, grant_types, is_active, created_at, updated_at)
VALUES
(@cli_bitcoin,   'bitcoin-web',   @secret_hash, 'Bitcoin Web',
 'Cliente web de Bitcoin',   JSON_ARRAY('https://bitcoin.msarknet.me/callback'), 'read',
 JSON_ARRAY('authorization_code','refresh_token'), 1, @now, @now),
(@cli_abogados,  'abogados-web',  @secret_hash, 'MsArkNet Lawyer',
 'Frontend de abogados',     JSON_ARRAY('https://lawyer.msarknet.me/callback'), 'read write',
 JSON_ARRAY('authorization_code','refresh_token'), 1, @now, @now),
(@cli_cerebrofe, 'cerebro-fe',    @secret_hash, 'Cerebro FE',
 'Frontend principal SSO',   JSON_ARRAY('https://msarknet.me/callback','http://localhost:5173/callback'), 'read write',
 JSON_ARRAY('authorization_code','refresh_token'), 1, @now, @now)
ON DUPLICATE KEY UPDATE
  name = VALUES(name),
  description = VALUES(description),
  redirect_uris = VALUES(redirect_uris),
  scope = VALUES(scope),
  grant_types = VALUES(grant_types),
  is_active = VALUES(is_active),
  updated_at = VALUES(updated_at);

INSERT INTO monitored_services
(name, container_name, url, icon, description, is_active, created_at, updated_at)
VALUES
('Traefik', 'traefik', 'https://traefik.msarknet.me', '⚙️', 'Panel de control del proxy reverso', 1, @now, @now),
('Adminer', 'adminer', 'https://adminer.msarknet.me', '🗄️', 'Administrador de base de datos', 1, @now, @now),
('MariaDB', 'mariadb', NULL, '🗃️', 'Base de datos MariaDB', 1, @now, @now),
('Cerebro Frontend', 'cerebro-fe', 'https://msarknet.me', '🧠', 'Frontend React de Cerebro', 1, @now, @now),
('Cerebro Backend', 'cerebro-be', 'https://api.msarknet.me', '⚡', 'Backend Node.js de Cerebro', 1, @now, @now),
('Webview Health Monitor', 'health-fe', 'https://status.msarknet.me', '🏥', 'Webview monitoreo de servicios', 1, @now, @now),
('API Health Monitor', 'health-be', 'https://api-status.msarknet.me', '⚡', 'API de monitoreo de servicios', 1, @now, @now),
('Portainer', 'portainer', 'https://portainer.msarknet.me', '⚙️', 'Panel de control de Docker', 0, @now, @now),
('Whoami', 'whoami', 'https://whoami.msarknet.me', '⚙️', 'Servicio de testing whoami', 0, @now, @now),
('Grafana', 'grafana', 'https://grafana.msarknet.me', '📊', 'Panel de monitorización', 0, @now, @now),
('Prometheus', 'prometheus', 'https://prom.msarknet.me', '📈', 'Servicio de métricas', 0, @now, @now),
('Mkdocs', 'mkdocs', 'https://docs.msarknet.me', '📚', 'Documentación del proyecto', 1, @now, @now)
ON DUPLICATE KEY UPDATE
  url = VALUES(url),
  description = VALUES(description),
  is_active = VALUES(is_active),
  updated_at = VALUES(updated_at);

-- Triggers removidos temporalmente para evitar problemas con DELIMITER
-- Se pueden agregar manualmente después si es necesario
