#!/usr/bin/env node

/**
 * Script de migración para configurar la base de datos
 * Ejecuta el archivo setup.sql en la base de datos MariaDB
 */

// Cargar variables de entorno
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// Configuración de la base de datos
const dbConfig = {
  host: process.env.DB_HOST || 'mariadb',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'health_monitor',
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4',
  timezone: '+00:00',
  multipleStatements: true // Permitir múltiples statements
};

async function runMigration() {
  let connection;

  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // Leer el archivo de setup
    const setupPath = path.join(__dirname, '../database/setup.sql');

    if (!fs.existsSync(setupPath)) {
      throw new Error(`Setup file not found: ${setupPath}`);
    }

    const setupSQL = fs.readFileSync(setupPath, 'utf8');
    console.log('📄 Setup SQL file loaded');

    // Ejecutar el script
    console.log('🔄 Running migration...');
    await connection.query(setupSQL);
    console.log('✅ Migration completed successfully');

    // Verificar que los datos se insertaron
    const [rows] = await connection.query('SELECT COUNT(*) as count FROM monitored_services');
    console.log(`📊 Services in database: ${rows[0].count}`);

    // Mostrar los servicios
    const [services] = await connection.query('SELECT name, container_name, url FROM monitored_services ORDER BY name');
    console.log('\n📋 Configured services:');
    services.forEach(service => {
      console.log(`   - ${service.name} (${service.container_name}) ${service.url ? '-> ' + service.url : ''}`);
    });

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Ejecutar migración
if (require.main === module) {
  console.log('🚀 Starting database migration...\n');
  runMigration();
}

module.exports = { runMigration };
